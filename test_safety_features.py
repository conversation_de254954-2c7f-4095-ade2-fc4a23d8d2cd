#!/usr/bin/env python3
"""
Test script for Mac AI Agent safety features
"""

import sys
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from mac_ai_agent import MacAIAgent


def test_environment_detection():
    """Test the agent's ability to detect its development environment."""
    print("🧪 Testing Environment Detection")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test current application identification
    print("1. Identifying current application...")
    app_info = agent.identify_current_application()
    
    if app_info["success"]:
        print(f"✅ Successfully identified current environment")
        print(f"📱 Protected app detected: {app_info['is_protected_app']}")
        print(f"🔍 Analysis preview: {app_info['analysis'][:150]}...")
        
        if app_info['is_protected_app']:
            print("🛡️ SAFETY: Development environment protection is ACTIVE")
        else:
            print("ℹ️ Current environment is not flagged as protected")
    else:
        print(f"❌ Failed to identify current environment: {app_info.get('error', 'Unknown error')}")
    
    print()


def test_dangerous_action_detection():
    """Test detection of potentially dangerous actions."""
    print("🧪 Testing Dangerous Action Detection")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test various action descriptions
    test_actions = [
        ("click close button", (20, 20)),
        ("open Chrome", None),
        ("close Visual Studio Code", None),
        ("quit Terminal", None),
        ("type hello world", None),
        ("click at center", (500, 400)),
        ("delete all files", None),
        ("shutdown computer", None)
    ]
    
    for action_desc, coords in test_actions:
        print(f"Testing: '{action_desc}'")
        safety_check = agent.screen.is_dangerous_action(action_desc, coords)
        
        risk_emoji = "🚨" if safety_check['risk_level'] == 'high' else ("⚠️" if safety_check['risk_level'] == 'medium' else "✅")
        print(f"  {risk_emoji} Risk Level: {safety_check['risk_level']}")
        print(f"  🛡️ Dangerous: {safety_check['is_dangerous']}")
        
        if safety_check['warning_message']:
            print(f"  📝 Warning: {safety_check['warning_message'][:100]}...")
        
        print()


def test_safe_app_opening():
    """Test safe application opening workflow."""
    print("🧪 Testing Safe App Opening")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test opening a safe application (Calculator)
    print("Testing safe app opening: Calculator")
    
    try:
        result = agent.open_application("Calculator")
        
        if result["success"]:
            print("✅ Calculator opening attempted successfully")
            print(f"📸 Screenshots taken: {len([k for k in result.keys() if 'screenshot' in k])}")
            
            # Wait a moment then take another screenshot to see if it opened
            time.sleep(3)
            final_check = agent.take_screenshot_with_timestamp(
                analyze=True, 
                description="Checking if Calculator opened successfully"
            )
            print(f"📸 Final verification screenshot: {final_check}")
            
        else:
            print(f"❌ Failed to open Calculator: {result.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error during app opening test: {e}")
    
    print()


def test_screenshot_analysis():
    """Test screenshot analysis capabilities."""
    print("🧪 Testing Screenshot Analysis")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test basic screen analysis
    print("1. Taking and analyzing current screen...")
    analysis = agent.analyze_screen("What applications are currently open and visible?")
    
    if analysis:
        print("✅ Screen analysis successful")
        print(f"🔍 Analysis: {analysis[:200]}...")
    else:
        print("❌ Screen analysis failed")
    
    print()


def test_keyboard_safety():
    """Test keyboard shortcut safety."""
    print("🧪 Testing Keyboard Safety")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test safe keyboard shortcuts
    safe_shortcuts = [
        ("cmd", "space"),  # Spotlight
        ("cmd", "tab"),    # App switcher
        ("cmd", "c"),      # Copy
        ("cmd", "v"),      # Paste
    ]
    
    dangerous_shortcuts = [
        ("cmd", "q"),      # Quit application
        ("cmd", "w"),      # Close window
        ("cmd", "option", "esc"),  # Force quit
    ]
    
    print("Testing safe shortcuts:")
    for shortcut in safe_shortcuts:
        action_desc = f"key combination {' + '.join(shortcut)}"
        safety_check = agent.screen.is_dangerous_action(action_desc)
        print(f"  {' + '.join(shortcut)}: {'✅ Safe' if not safety_check['is_dangerous'] else '⚠️ Flagged'}")
    
    print("\nTesting potentially dangerous shortcuts:")
    for shortcut in dangerous_shortcuts:
        action_desc = f"key combination {' + '.join(shortcut)}"
        safety_check = agent.screen.is_dangerous_action(action_desc)
        print(f"  {' + '.join(shortcut)}: {'🚨 Dangerous' if safety_check['is_dangerous'] else '✅ Safe'}")
    
    print()


def main():
    """Run all safety tests."""
    print("🤖 Mac AI Agent Safety Features Test Suite")
    print("=" * 60)
    print()
    
    try:
        test_environment_detection()
        test_dangerous_action_detection()
        test_screenshot_analysis()
        test_keyboard_safety()
        
        # Only test app opening if user confirms
        print("⚠️ The next test will attempt to open Calculator.")
        confirm = input("Do you want to proceed? (y/n): ").lower().strip()
        if confirm in ['y', 'yes']:
            test_safe_app_opening()
        else:
            print("⏭️ Skipping app opening test")
        
        print("🎉 All safety tests completed!")
        print("\n🛡️ Safety Features Summary:")
        print("✅ Environment detection")
        print("✅ Dangerous action detection")
        print("✅ Protected app awareness")
        print("✅ Screenshot analysis")
        print("✅ Keyboard shortcut safety")
        
    except KeyboardInterrupt:
        print("\n\n👋 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")


if __name__ == "__main__":
    main()
