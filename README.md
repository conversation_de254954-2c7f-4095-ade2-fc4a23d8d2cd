# Mac AI Agent 🤖

An intelligent automation agent for macOS that can interact with your screen through clicking, dragging, taking screenshots, typing, and using keyboard shortcuts. Powered by local LLM via LM Studio.

## 🚀 Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start LM Studio** with a model loaded (your Phi-3 model is perfect!)

3. **Grant accessibility permissions** (System Preferences → Security & Privacy → Privacy → Accessibility)

4. **Run the agent:**
   ```bash
   python chat_agent.py
   ```

5. **Start chatting:**
   ```
   👤 You: open Safari
   👤 You: take a screenshot
   👤 You: search for Calculator
   ```

## Features

- **🧠 Local LLM Brain**: Uses LM Studio for intelligent decision-making with AI vision
- **🌐 Real-time Information**: Perplexity integration for current news, weather, stocks
- **🕐 Time & Timezone Aware**: East Africa Time (UTC+3) default, world clock support
- **👁️ Visual Verification**: Takes screenshots before/after actions to verify success
- **💬 Natural Language**: Chat interface for easy interaction
- **🖱️ Screen Interaction**: Click, drag, and interact with UI elements
- **📸 Screenshot Capture**: Take and analyze screenshots automatically with AI vision
- **⌨️ Keyboard Control**: Type text and use keyboard shortcuts (Cmd+Tab, etc.)
- **🔍 App Search**: Open applications via Spotlight search (Cmd+Space)
- **🛡️ Development Environment Protection**: Automatically protects VS Code, Terminal, IDEs
- **⚠️ Safety Checks**: Warns before dangerous actions, requires confirmation for high-risk operations
- **🤖 Intelligent Automation**: Understands screen content and makes smart decisions

## 🛡️ Safety Features

The Mac AI Agent includes comprehensive safety features to protect your development environment:

### Development Environment Protection
- **Automatic Detection**: Identifies when VS Code, Terminal, or other IDEs are active
- **Protected Applications**: Prevents accidental closure of development tools
- **Smart Warnings**: Alerts you before potentially dangerous actions

### Action Safety Checks
- **Risk Assessment**: Evaluates each action for potential danger
- **Confirmation Required**: High-risk actions require explicit user confirmation
- **Coordinate Safety**: Warns when clicking near window close buttons
- **Keyword Detection**: Identifies dangerous terms like "quit", "delete", "close"

### Visual Verification
- **AI Vision Analysis**: Uses LLM vision to understand screen content
- **Before/After Screenshots**: Captures screen state to verify action success
- **Application Identification**: Recognizes which apps are currently active
- **Success Verification**: Confirms that intended actions completed successfully

## Requirements

- macOS (tested on M1 Air)
- Python 3.8+
- LM Studio with a loaded model
- Node.js and npm (for Perplexity integration)
- Accessibility permissions for screen control

## Installation

### Quick Setup
Run the setup script for guided installation:
```bash
python setup_agent.py
```

### Manual Setup
1. Create a virtual environment:
   ```bash
   # Using conda (recommended)
   conda create -n mac-ai-agent python=3.10
   conda activate mac-ai-agent

   # Or using venv
   python3 -m venv venv
   source venv/bin/activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Setup

### 1. Accessibility Permissions
Grant accessibility permissions for screen automation:

1. Go to **System Preferences → Security & Privacy → Privacy → Accessibility**
2. Click the lock icon and enter your password
3. Add your terminal application (Terminal.app or iTerm.app)
4. Add Python (find your Python executable path)
5. Enable checkboxes for both applications

### 2. LM Studio Setup (Recommended)
For the AI brain functionality:

1. Download [LM Studio](https://lmstudio.ai/)
2. Install and open LM Studio
3. Download a model (recommended: Llama 2 7B or similar)
4. Load the model in LM Studio
5. Start the local server (usually http://localhost:1234)

The agent works with limited functionality without LM Studio, but you'll get the best experience with a local LLM.

## Usage

### Terminal Chat Interface (Recommended)
Start the interactive chat agent:
```bash
python chat_agent.py
```

The agent will:
- 🤖 Chat with you using natural language
- 📸 Take screenshots when needed
- 🔍 Help you search and open applications
- ⌨️ Execute keyboard shortcuts and typing
- 🖱️ Perform mouse clicks and drags
- 🧠 Use local LLM (via LM Studio) for intelligent responses

### Example Conversations
```
👤 You: open Safari
🤖 Agent: 📸 Taking screenshots before/during/after opening Safari...
         ✅ Attempted to open Safari - check screenshots for verification
         🔍 Visual Verification: Safari appears to have opened successfully...

👤 You: open chrome
🤖 Agent: 📸 Current state captured, opening Google Chrome...
         ✅ Attempted to open Google Chrome - check screenshots for verification
         🔍 Visual Verification: Chrome window is now visible on screen...

👤 You: take a screenshot
🤖 Agent: ✅ Screenshot saved to screenshots/screenshot_1234567890.png

👤 You: what's the latest news about AI?
🤖 Agent: 🌐 [Real-time Info] Getting current AI news from Perplexity...

👤 You: what's the weather today?
🤖 Agent: 🌐 [Real-time Info] Current weather information...

👤 You: what time is it?
🤖 Agent: 🕐 [Time Info] Current time: 15:30:45 EAT (Saturday, July 5, 2025)

👤 You: what time is it in New York?
🤖 Agent: 🕐 [Time Info] New York time: 08:30:45 EST...
```

### Programmatic Usage
```python
from mac_ai_agent import MacAIAgent

# Create an agent instance
agent = MacAIAgent()

# Take a screenshot
screenshot_path = agent.take_screenshot_with_timestamp()

# Chat with the agent
response = agent.chat("open Calculator app")

# Direct screen interactions
agent.screen.click(100, 200)
agent.screen.type_text("Hello, World!")
agent.screen.key_combo("cmd", "tab")
```

### Examples
Run the example demos:
```bash
python examples/basic_usage.py
```

## 🏗️ Architecture

### System Overview
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Terminal      │    │   Mac AI Agent   │    │   LM Studio     │
│   Chat UI       │◄──►│   (Core Brain)   │◄──►│   Local LLM     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ├──────────────────┐
                                ▼                  ▼
                       ┌──────────────────┐ ┌─────────────────┐ ┌─────────────────┐
                       │  Screen Control  │ │   Perplexity    │ │   Time Tool     │
                       │  • PyAutoGUI     │ │   Real-time     │ │   • EAT Default │
                       │  • macOS APIs    │ │   Information   │ │   • World Clock │
                       │  • Screenshots   │ │   • News        │ │   • Timezones   │
                       └──────────────────┘ │   • Weather     │ │   • Date/Time   │
                                            │   • Stocks      │ └─────────────────┘
                                            └─────────────────┘
```

### Core Components

#### 1. **MacAIAgent** (`core.py`)
- **Main orchestrator** that combines all capabilities
- **Conversation management** with history tracking
- **Action planning** using LM Studio for intelligent decisions
- **Safety controls** with user confirmation before actions

#### 2. **ScreenInteraction** (`screen_interaction.py`)
- **Mouse control**: Click, drag, move, scroll
- **Keyboard automation**: Type text, key combinations, shortcuts
- **Screenshot capture**: Full screen or regions
- **Image recognition**: Find UI elements by image matching
- **Pixel analysis**: Color detection and verification

#### 3. **LMStudioClient** (`lm_studio_client.py`)
- **OpenAI-compatible API** client for LM Studio
- **Chat completions** with conversation context
- **Specialized prompts** for Mac automation tasks
- **Error handling** and connection management

#### 4. **PerplexityTool** (`perplexity_tool.py`)
- **Real-time information** access via Perplexity API
- **Current events** - news, weather, stock prices
- **NPX integration** - uses server-perplexity-ask package
- **Smart detection** - automatically triggers for current info requests

#### 5. **TimeTool** (`time_tool.py`)
- **Time awareness** with East Africa Time (UTC+3) as default
- **Multiple timezones** - world clock, specific timezone queries
- **Natural language** - "what time is it?", "time in Tokyo"
- **Date information** - current date, day of week, etc.

#### 6. **Visual Verification System**
- **Before/After Screenshots** - Captures screen state before and after each action
- **LLM Analysis** - Uses local LLM to analyze screenshots and verify success
- **Action Verification** - Confirms if apps actually opened, actions succeeded
- **Automatic Documentation** - All screenshots saved with timestamps for review

#### 7. **Terminal Chat Interface** (`chat_agent.py`)
- **Interactive CLI** with rich formatting
- **Command processing** (help, status, screenshot, time, worldclock, etc.)
- **Graceful interruption** handling (Ctrl+C)
- **Session management** with conversation saving/loading

### Supported Models

The agent works with any model supported by LM Studio. **Recommended models:**

- ✅ **Phi-3 Mini** (your current model) - Excellent for automation tasks
- ✅ **Llama 2 7B/13B** - Great balance of performance and capability
- ✅ **Code Llama** - Enhanced for technical tasks
- ✅ **Mistral 7B** - Fast and efficient
- ✅ **Qwen** - Good multilingual support

**Model Requirements:**
- **Minimum**: 4GB RAM models (Phi-3 Mini, Llama 2 7B)
- **Recommended**: 8GB+ RAM models for better reasoning
- **Context length**: 4K+ tokens (your Phi-3 has 128K!)

### Expected Capabilities

#### ✅ **What the Agent Can Do:**
- **Application control**: Open, switch, close applications (direct execution)
- **Text automation**: Type in any text field, forms, documents
- **Navigation**: Click buttons, links, menu items
- **File operations**: Open files, save documents (via UI)
- **System shortcuts**: Cmd+Tab, Cmd+C/V, Spotlight search
- **Screen analysis**: Describe what's visible, find elements
- **Multi-step workflows**: Chain actions together intelligently
- **Instant execution**: Common commands like "open Chrome" execute immediately

#### ⚠️ **Current Limitations:**
- **No direct file system access** (works through UI only)
- **Limited OCR** (basic text recognition, not advanced vision)
- **Coordinate-based clicking** (may need adjustment for different screens)
- **No browser automation** (use dedicated tools like Selenium for complex web tasks)
- **Single display support** (multi-monitor setups may need configuration)

#### 🔮 **Future Enhancements:**
- **Computer vision models** for better UI element detection
- **Voice control** integration
- **Workflow recording** and playback
- **Multi-monitor support**
- **Advanced OCR** with text extraction and analysis

## Safety & Best Practices

⚠️ **Important Safety Information**

This tool can control your computer. Please follow these safety guidelines:

### Before Using
- **Test in a safe environment** - Try on a test machine or with non-critical applications first
- **Save your work** - Close or save important documents before running automation
- **Understand the commands** - Review what the agent plans to do before approving actions
- **Keep PyAutoGUI failsafe enabled** - Move mouse to top-left corner to emergency stop

### Built-in Safety Features
- **Action confirmation** - Agent shows you the plan before executing
- **Screenshot logging** - All screenshots are saved for review
- **Graceful interruption** - Ctrl+C stops the agent safely
- **Limited scope** - Agent only performs actions you explicitly approve

### Recommended Practices
1. Start with simple commands like "take a screenshot"
2. Use the chat interface rather than direct automation calls
3. Keep LM Studio running for intelligent decision-making
4. Review the conversation history periodically
5. Test new commands in a controlled environment

### Troubleshooting
- **"Permission denied"** → Check accessibility permissions
- **"LM Studio not found"** → Start LM Studio and load a model
- **"Agent not responding"** → Check terminal for error messages
- **"Actions not working"** → Verify screen coordinates and timing

## Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Acknowledgments

- Built with PyAutoGUI for screen automation
- Powered by LM Studio for local LLM integration
- Designed for macOS with native system integration
