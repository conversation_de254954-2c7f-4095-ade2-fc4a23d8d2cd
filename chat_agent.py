#!/usr/bin/env python3
"""
Terminal-based chat interface for Mac AI Agent
"""

import sys
import os
import signal
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from mac_ai_agent import MacAIAgent


class TerminalChatInterface:
    """
    Terminal-based chat interface for interacting with the Mac AI Agent.
    """
    
    def __init__(self):
        """Initialize the chat interface."""
        self.agent = None
        self.running = True
        
        # Set up signal handler for graceful exit
        signal.signal(signal.SIGINT, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle Ctrl+C gracefully."""
        print("\n\n👋 Goodbye!")
        self.running = False
        sys.exit(0)
    
    def print_banner(self):
        """Print the welcome banner."""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                     🤖 Mac AI Agent                         ║
║                                                              ║
║  Your intelligent Mac automation assistant powered by        ║
║  local LLM through LM Studio                                 ║
║                                                              ║
║  I can help you:                                             ║
║  • 🔍 Search and open applications (Cmd+Space)              ║
║  • 🖱️  Click on UI elements with safety checks              ║
║  • ⌨️  Type text and use keyboard shortcuts                  ║
║  • 📸 Take and analyze screenshots with AI vision           ║
║  • 🔄 Switch between apps (Cmd+Tab)                          ║
║  • 🖱️  Drag and drop items                                   ║
║  • 🌐 Get real-time info (news, weather, stocks)            ║
║  • 🕐 Time and timezone awareness (EAT default)             ║
║  • 🛡️  Protect your development environment                  ║
║                                                              ║
║  Type 'help' for commands or just tell me what to do!       ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def print_help(self):
        """Print help information."""
        help_text = """
🆘 Available Commands:

Basic Commands:
  help              - Show this help message
  status            - Show agent status
  screenshot        - Take a screenshot
  time              - Show current time (East Africa Time)
  worldclock        - Show world clock
  clear             - Clear conversation history
  quit/exit         - Exit the chat

Action Commands (examples):
  "open Safari"                    - Open Safari browser
  "search for Calculator"          - Search for Calculator app
  "take a screenshot"              - Capture current screen
  "switch to next app"             - Use Cmd+Tab to switch apps
  "click at 100 200"              - Click at coordinates (100, 200)
  "type hello world"              - Type text
  "press cmd+c"                   - Use keyboard shortcut

Visual Clicking (NEW!):
  "click the red button"           - AI finds and clicks the red button
  "click on the search box"        - AI finds and clicks the search box
  "press the submit button"        - AI finds and clicks submit button
  "tap the Chrome icon"            - AI finds and clicks Chrome icon
  "click the close button"         - AI finds close button (safety protected)

Real-time Information (examples):
  "what's the weather today?"      - Get current weather
  "latest news about AI"           - Get recent AI news
  "Apple stock price"              - Get current stock price
  "what's happening today?"        - Get current events

Time & Date (examples):
  "what time is it?"               - Current time (East Africa Time)
  "what's the date today?"         - Current date
  "world clock"                    - Time in major cities
  "time in New York"               - Time in specific timezone

Natural Language:
  Just describe what you want to do! Examples:
  • "I want to open my email app"
  • "Can you help me find the settings?"
  • "Take a screenshot and tell me what you see"
  • "Click the red button on the screen"
  • "Press the search button"
  • "What's the latest news about Tesla?"
  • "What time is it in Tokyo?"
  • "Switch to my browser"

🛡️  Safety Features:
  • Automatically detects your development environment
  • Protects VS Code, Terminal, and other dev tools
  • Warns before dangerous actions (close, quit, delete)
  • Requires confirmation for high-risk operations
  • Takes screenshots to verify actions succeeded
  • Uses AI vision to understand what's on screen
        """
        print(help_text)
    
    def initialize_agent(self):
        """Initialize the Mac AI Agent."""
        print("🚀 Initializing Mac AI Agent...")
        
        try:
            self.agent = MacAIAgent()
            status = self.agent.get_status()
            
            print(f"✅ Agent initialized successfully!")
            print(f"   Screen size: {status['screen_size'][0]}x{status['screen_size'][1]}")
            
            if status['lm_studio']['running']:
                models = status['lm_studio']['models']
                if models:
                    print(f"✅ LM Studio connected with {len(models)} model(s)")
                else:
                    print("⚠️  LM Studio connected but no models loaded")
            else:
                print("⚠️  LM Studio not detected - some features may be limited")
                print("   Please start LM Studio and load a model for full functionality")

            # Show Perplexity status
            if status['perplexity']['success']:
                print("✅ Perplexity integration ready for real-time information")
            else:
                print(f"⚠️  Perplexity limited: {status['perplexity']['error']}")
                if "suggestion" in status['perplexity']:
                    print(f"   {status['perplexity']['suggestion']}")

            # Show time tool status
            if status['time_tool']['success']:
                print(f"✅ Time tool ready - Current: {status['time_tool']['formatted']}")
            else:
                print(f"⚠️  Time tool limited: {status['time_tool'].get('error', 'Unknown error')}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize agent: {e}")
            return False
    
    def process_command(self, user_input: str) -> bool:
        """
        Process user commands and return whether to continue.
        
        Args:
            user_input: User's input
            
        Returns:
            True to continue, False to exit
        """
        command = user_input.lower().strip()
        
        # Handle special commands
        if command in ['quit', 'exit', 'q']:
            return False
        
        elif command == 'help':
            self.print_help()
            return True
        
        elif command == 'status':
            status = self.agent.get_status()
            print("\n📊 Agent Status:")
            print(f"   Screen: {status['screen_size'][0]}x{status['screen_size'][1]}")
            print(f"   LM Studio: {'✅ Connected' if status['lm_studio']['running'] else '❌ Disconnected'}")
            print(f"   Models: {len(status['lm_studio']['models'])}")
            print(f"   Perplexity: {'✅ Ready' if status['perplexity']['success'] else '❌ Limited'}")
            print(f"   Time Tool: {'✅ Ready' if status['time_tool']['success'] else '❌ Limited'}")
            if status['time_tool']['success']:
                print(f"   Current Time: {status['time_tool']['formatted']}")
            print(f"   Conversation: {status['conversation_length']} messages")
            print(f"   Screenshots: {status['screenshots_dir']}")
            return True
        
        elif command == 'clear':
            self.agent.clear_conversation()
            return True
        
        elif command == 'screenshot':
            path = self.agent.take_screenshot_with_timestamp()
            print(f"📸 Screenshot saved: {path}")
            return True

        elif command == 'time':
            time_data = self.agent.time_tool.get_current_time()
            time_response = self.agent.time_tool.format_time_response(time_data)
            print(f"\n{time_response}")
            return True

        elif command == 'worldclock':
            world_data = self.agent.time_tool.get_world_clock()
            world_response = self.agent.time_tool.format_time_response(world_data)
            print(f"\n{world_response}")
            return True
        
        # Handle regular chat/action requests
        else:
            try:
                print("\n🤖 Processing your request...")
                response = self.agent.chat(user_input)
                print(f"\n🤖 Agent: {response}")
                
            except Exception as e:
                print(f"❌ Error processing request: {e}")
        
        return True
    
    def run(self):
        """Run the main chat loop."""
        self.print_banner()
        
        # Initialize the agent
        if not self.initialize_agent():
            print("❌ Failed to start. Please check your setup and try again.")
            return
        
        print("\n💬 Chat started! Type your message or 'help' for commands.")
        print("   Press Ctrl+C to exit anytime.\n")
        
        # Main chat loop
        while self.running:
            try:
                # Get user input
                user_input = input("👤 You: ").strip()
                
                if not user_input:
                    continue
                
                # Process the command
                should_continue = self.process_command(user_input)
                
                if not should_continue:
                    break
                
                print()  # Add spacing between interactions
                
            except EOFError:
                # Handle Ctrl+D
                break
            except KeyboardInterrupt:
                # Handle Ctrl+C
                break
        
        print("\n👋 Thanks for using Mac AI Agent!")


def main():
    """Main entry point."""
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    # Create and run the chat interface
    chat = TerminalChatInterface()
    chat.run()


if __name__ == "__main__":
    main()
