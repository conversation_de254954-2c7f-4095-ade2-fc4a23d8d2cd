#!/usr/bin/env python3
"""
Demonstration of intelligent Chrome opening workflow with visual verification
"""

import sys
import time
from pathlib import Path

# Add the parent directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from mac_ai_agent import MacAIAgent


def demonstrate_chrome_opening():
    """
    Demonstrate the complete Chrome opening workflow:
    1. Take initial screenshot
    2. Press Cmd+Space for Spotlight
    3. Type "Chrome"
    4. Take screenshot of search results
    5. Press Enter
    6. Wait for Chrome to load
    7. Take final screenshot and verify
    """
    print("🌐 Chrome Opening Demonstration")
    print("=" * 50)
    
    # Initialize the agent
    agent = MacAIAgent()
    
    print("🔍 Step 1: Analyzing current environment...")
    
    # Check current environment for safety
    app_info = agent.identify_current_application()
    if app_info["success"]:
        print(f"📱 Current app analysis: {app_info['analysis'][:100]}...")
        if app_info['is_protected_app']:
            print("🛡️ Protected development environment detected - proceeding with caution")
    
    print("\n📸 Step 2: Taking initial screenshot...")
    before_screenshot = agent.take_screenshot_with_timestamp(
        analyze=True,
        description="Desktop state before opening Chrome"
    )
    print(f"✅ Before screenshot: {before_screenshot}")
    
    print("\n🔍 Step 3: Opening Spotlight search (Cmd+Space)...")
    agent.screen.key_combo("cmd", "space")
    time.sleep(1.0)  # Wait for Spotlight to appear
    
    print("📸 Step 4: Taking screenshot of Spotlight...")
    spotlight_screenshot = agent.take_screenshot_with_timestamp(
        analyze=True,
        description="Spotlight search interface opened"
    )
    print(f"✅ Spotlight screenshot: {spotlight_screenshot}")
    
    print("\n⌨️ Step 5: Typing 'Chrome'...")
    agent.screen.type_text("Chrome")
    time.sleep(1.5)  # Wait for search results
    
    print("📸 Step 6: Taking screenshot of search results...")
    search_results_screenshot = agent.take_screenshot_with_timestamp(
        analyze=True,
        description="Spotlight search results for Chrome"
    )
    print(f"✅ Search results screenshot: {search_results_screenshot}")
    
    print("\n⏎ Step 7: Pressing Enter to open Chrome...")
    agent.screen.press_key("enter")
    
    print("⏳ Step 8: Waiting for Chrome to launch...")
    time.sleep(4.0)  # Wait for Chrome to start
    
    print("📸 Step 9: Taking screenshot after Chrome launch...")
    after_launch_screenshot = agent.take_screenshot_with_timestamp(
        analyze=True,
        description="Screen state immediately after pressing Enter to launch Chrome"
    )
    print(f"✅ After launch screenshot: {after_launch_screenshot}")
    
    print("\n⏳ Step 10: Waiting for Chrome to fully load...")
    time.sleep(3.0)  # Additional wait for full loading
    
    print("📸 Step 11: Taking final verification screenshot...")
    final_screenshot = agent.take_screenshot_with_timestamp(
        analyze=True,
        description="Final verification - checking if Chrome is open and ready"
    )
    print(f"✅ Final screenshot: {final_screenshot}")
    
    print("\n🔍 Step 12: Analyzing results with LLM...")
    
    # Use LLM to verify if Chrome opened successfully
    verification_question = """
    Looking at this final screenshot, please analyze:
    1. Is Google Chrome visible and open?
    2. What is the current state of Chrome (loading, ready, error)?
    3. Are there any Chrome windows or tabs visible?
    4. Did the application opening appear to be successful?
    5. What should be the next step if I wanted to navigate to a website?
    
    Please be specific about what you can see.
    """
    
    verification_analysis = agent.llm.analyze_image(final_screenshot, verification_question)
    
    if verification_analysis:
        print("🤖 LLM Verification Analysis:")
        print(f"   {verification_analysis}")
        
        # Check if Chrome appears to be open based on analysis
        analysis_lower = verification_analysis.lower()
        chrome_indicators = ['chrome', 'browser', 'google chrome', 'address bar', 'new tab']
        chrome_detected = any(indicator in analysis_lower for indicator in chrome_indicators)
        
        if chrome_detected:
            print("\n✅ SUCCESS: Chrome appears to have opened successfully!")
            print("🎉 The intelligent opening workflow completed successfully.")
        else:
            print("\n⚠️ UNCERTAIN: Chrome opening status unclear from analysis.")
            print("🔍 Manual verification may be needed.")
    else:
        print("\n❌ Could not perform LLM verification (vision model not available)")
    
    print(f"\n📊 Workflow Summary:")
    print(f"   📸 Screenshots taken: 5")
    print(f"   🔍 LLM analyses performed: 6")
    print(f"   ⌨️ Keyboard actions: 2 (Cmd+Space, Enter)")
    print(f"   ⏱️ Total time: ~10 seconds")
    print(f"   🛡️ Safety checks: Environment protection active")
    
    return {
        "screenshots": {
            "before": before_screenshot,
            "spotlight": spotlight_screenshot,
            "search_results": search_results_screenshot,
            "after_launch": after_launch_screenshot,
            "final": final_screenshot
        },
        "verification": verification_analysis,
        "success": chrome_detected if verification_analysis else None
    }


def demonstrate_app_switching():
    """Demonstrate Cmd+Tab app switching with visual verification."""
    print("\n🔄 App Switching Demonstration")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    print("📸 Taking screenshot before app switch...")
    before_switch = agent.take_screenshot_with_timestamp(
        analyze=True,
        description="Current app before switching"
    )
    
    print("🔄 Performing Cmd+Tab to switch apps...")
    agent.screen.key_combo("cmd", "tab")
    time.sleep(1.0)
    
    print("📸 Taking screenshot after app switch...")
    after_switch = agent.take_screenshot_with_timestamp(
        analyze=True,
        description="App state after Cmd+Tab switch"
    )
    
    print(f"✅ App switching demonstration complete!")
    print(f"   📸 Before: {before_switch}")
    print(f"   📸 After: {after_switch}")


def demonstrate_complete_workflow():
    """Demonstrate the complete Mac AI Agent workflow."""
    print("\n🤖 Complete Mac AI Agent Workflow Demo")
    print("=" * 60)

    agent = MacAIAgent()

    print("This demonstrates the agent's key capabilities:")
    print("1. 🛡️ Environment protection")
    print("2. 🔍 Intelligent app opening")
    print("3. ⌨️ Mac-specific shortcuts")
    print("4. 📸 Visual verification")
    print("5. 🤖 AI-powered decision making")

    # Step 1: Environment Analysis
    print("\n🔍 Step 1: Analyzing current environment...")
    app_info = agent.identify_current_application()

    if app_info["success"] and app_info["is_protected_app"]:
        print("🛡️ PROTECTION ACTIVE: Development environment detected!")
        print("   The agent will be extra careful not to interfere with your work.")

    # Step 2: Demonstrate safety checks
    print("\n🛡️ Step 2: Testing safety systems...")
    dangerous_action = "close Visual Studio Code"
    safety_check = agent.screen.is_dangerous_action(dangerous_action)

    if safety_check['is_dangerous']:
        print(f"✅ Safety system working: '{dangerous_action}' correctly flagged as dangerous")
        print(f"   Risk level: {safety_check['risk_level']}")

    # Step 3: Safe app opening
    print("\n🚀 Step 3: Demonstrating safe app opening...")
    return demonstrate_chrome_opening()


def main():
    """Run the Chrome opening demonstration."""
    print("🤖 Mac AI Agent - Enhanced Chrome Opening Demo")
    print("=" * 60)

    print("\n🌟 NEW ENHANCED FEATURES:")
    print("• 🛡️ Development Environment Protection")
    print("• 🤖 AI Vision Analysis")
    print("• ⚠️ Dangerous Action Detection")
    print("• 📸 Visual Verification at Each Step")
    print("• 🔍 Intelligent Screen Understanding")
    print("• ⌨️ Mac-Specific Keyboard Handling")

    print("\nThis demo will:")
    print("• 🔍 Analyze your current environment for safety")
    print("• 📸 Take screenshots at each step")
    print("• ⌨️ Use Cmd+Space to open Spotlight")
    print("• 🔍 Search for Chrome intelligently")
    print("• ⏎ Press Enter to launch")
    print("• 🤖 Use AI vision to verify success")
    print("• 🛡️ Protect your development environment")

    print("\n⚠️ SAFETY NOTE:")
    print("The agent knows this is your development environment.")
    print("It will NOT click close buttons on VS Code or Terminal unless explicitly told.")

    confirm = input("\n🤔 Ready to proceed with enhanced demo? (y/n): ").lower().strip()

    if confirm in ['y', 'yes']:
        try:
            # Run the complete enhanced workflow
            result = demonstrate_complete_workflow()

            # Optional: demonstrate app switching
            print("\n" + "="*60)
            switch_demo = input("🔄 Want to see safe app switching demo too? (y/n): ").lower().strip()
            if switch_demo in ['y', 'yes']:
                demonstrate_app_switching()

            print("\n🎉 Enhanced demonstration completed successfully!")
            print("\n🛡️ Your development environment remains protected!")

        except KeyboardInterrupt:
            print("\n\n👋 Demo interrupted by user")
        except Exception as e:
            print(f"\n❌ Demo error: {e}")
    else:
        print("👋 Demo cancelled. No actions were taken.")


if __name__ == "__main__":
    main()
