#!/usr/bin/env python3
"""
Demonstration of AI Vision-Based Clicking System
"""

import sys
import time
from pathlib import Path

# Add the parent directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from mac_ai_agent import MacAIAgent


def demonstrate_visual_clicking():
    """Demonstrate the AI vision-based clicking system."""
    print("🎯 AI Vision-Based Clicking Demo")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    print("This demo shows how the agent can:")
    print("• 📸 Take a screenshot")
    print("• 🤖 Use AI vision to identify UI elements")
    print("• 📍 Calculate precise click coordinates")
    print("• 🖱️ Perform the click with PyAutoGUI")
    print("• ✅ Verify the click worked")
    
    # Check if LLM vision is available
    test_screenshot = agent.take_screenshot_with_timestamp()
    test_analysis = agent.llm.analyze_image(test_screenshot, "Can you see this screenshot?")
    
    if not test_analysis:
        print("\n⚠️ LLM Vision not available - this demo requires a vision-capable model")
        print("Please ensure LM Studio is running with a vision model (like <PERSON>lava)")
        return
    
    print(f"\n✅ LLM Vision available! Test analysis: {test_analysis[:100]}...")
    
    # Demo different types of visual clicks
    demo_scenarios = [
        {
            "description": "Dock icon",
            "instruction": "Try to identify and click on an application icon in the dock",
            "safe": True
        },
        {
            "description": "menu bar item",
            "instruction": "Try to identify and click on a menu bar item",
            "safe": True
        },
        {
            "description": "window button",
            "instruction": "Try to identify a window control button (but will be blocked for safety)",
            "safe": False
        }
    ]
    
    for i, scenario in enumerate(demo_scenarios, 1):
        print(f"\n🎯 Scenario {i}: Visual Click on {scenario['description']}")
        print("-" * 40)
        
        target = input(f"What {scenario['description']} would you like to click on? (or 'skip'): ").strip()
        
        if target.lower() == 'skip':
            print("⏭️ Skipping this scenario")
            continue
        
        if not target:
            print("❌ No target specified, skipping")
            continue
        
        print(f"\n🔍 Looking for: '{target}'")
        print("📸 Taking screenshot and analyzing...")
        
        # Perform the visual click
        result = agent.screen.visual_click_with_verification(target, llm_client=agent.llm)
        
        if result["success"]:
            print(f"✅ Successfully clicked on '{target}'!")
            print(f"   📍 Coordinates: {result.get('coordinates', 'N/A')}")
            print(f"   🔍 Analysis: {result.get('analysis', '')[:100]}...")
            if result.get('verification'):
                print(f"   ✅ Verification: {result['verification'][:100]}...")
        else:
            print(f"❌ Failed to click on '{target}': {result['message']}")
            if 'safety' in result.get('message', '').lower():
                print("   🛡️ This was blocked by safety features (as expected)")


def demonstrate_natural_language_clicking():
    """Demonstrate natural language click commands."""
    print("\n💬 Natural Language Clicking Demo")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    print("You can now use natural language to click on things!")
    print("Examples:")
    print("• 'click the red button'")
    print("• 'click on the search box'")
    print("• 'press the submit button'")
    print("• 'tap the Chrome icon'")
    
    while True:
        print("\n" + "-" * 30)
        command = input("🗣️ Enter a click command (or 'done' to finish): ").strip()
        
        if command.lower() in ['done', 'exit', 'quit']:
            break
        
        if not command:
            continue
        
        print(f"\n🎯 Processing: '{command}'")
        
        # Use the agent's natural language processing
        result = agent.execute_simple_action(command)
        
        if result:
            if result["success"]:
                print(f"✅ {result['message']}")
                if result.get('coordinates'):
                    print(f"   📍 Clicked at: {result['coordinates']}")
            else:
                print(f"❌ {result['message']}")
                if 'safety' in result.get('message', '').lower():
                    print("   🛡️ Blocked by safety features")
        else:
            print("❓ Command not recognized as a click action")


def demonstrate_coordinate_parsing():
    """Demonstrate how the system parses coordinates from LLM analysis."""
    print("\n📍 Coordinate Parsing Demo")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test coordinate parsing with various formats
    test_analyses = [
        "I can see the button. Estimated click coordinates: X: 45%, Y: 30%",
        "The element is located at x: 60%, y: 25% of the screen",
        "Click coordinates should be around 50% horizontally and 40% vertically",
        "The button is at pixel coordinates (500, 300)",
        "Located at position (800, 200) on the screen"
    ]
    
    print("Testing coordinate parsing from different LLM response formats:")
    
    for i, analysis in enumerate(test_analyses, 1):
        print(f"\n{i}. Analysis: '{analysis}'")
        coords = agent.screen._parse_coordinates_from_analysis(analysis)
        
        if coords:
            x, y = coords
            x_percent = (x / agent.screen.screen_width) * 100
            y_percent = (y / agent.screen.screen_height) * 100
            print(f"   ✅ Parsed coordinates: ({x}, {y}) = ({x_percent:.1f}%, {y_percent:.1f}%)")
        else:
            print("   ❌ Could not parse coordinates")


def main():
    """Run the visual clicking demonstration."""
    print("🤖 Mac AI Agent - Visual Clicking System Demo")
    print("=" * 60)
    
    print("\n🌟 NEW FEATURE: AI Vision-Based Clicking!")
    print("The agent can now:")
    print("• 👁️ See and understand your screen")
    print("• 🎯 Find UI elements by description")
    print("• 📍 Calculate precise click coordinates")
    print("• 🖱️ Click exactly where needed")
    print("• ✅ Verify the click worked")
    print("• 🛡️ Maintain safety protections")
    
    print("\n⚠️ REQUIREMENTS:")
    print("• LM Studio running with a vision-capable model (like Llava)")
    print("• The model should be able to analyze images")
    
    confirm = input("\n🤔 Ready to test visual clicking? (y/n): ").lower().strip()
    
    if confirm in ['y', 'yes']:
        try:
            # Test coordinate parsing first
            demonstrate_coordinate_parsing()
            
            # Test visual clicking
            demo_visual = input("\n🎯 Test visual clicking system? (y/n): ").lower().strip()
            if demo_visual in ['y', 'yes']:
                demonstrate_visual_clicking()
            
            # Test natural language commands
            demo_nl = input("\n💬 Test natural language click commands? (y/n): ").lower().strip()
            if demo_nl in ['y', 'yes']:
                demonstrate_natural_language_clicking()
            
            print("\n🎉 Visual clicking demo completed!")
            print("\n🎯 You can now use commands like:")
            print("• 'click the red button'")
            print("• 'click on the search box'")
            print("• 'press the submit button'")
            print("• 'tap the Chrome icon'")
            print("\nThe agent will use AI vision to find and click on these elements!")
            
        except KeyboardInterrupt:
            print("\n\n👋 Demo interrupted by user")
        except Exception as e:
            print(f"\n❌ Demo error: {e}")
    else:
        print("👋 Demo cancelled. No actions were taken.")


if __name__ == "__main__":
    main()
