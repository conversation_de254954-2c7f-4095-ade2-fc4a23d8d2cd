#!/usr/bin/env python3
"""
Comprehensive test for the enhanced Mac AI Agent system
"""

import sys
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from mac_ai_agent import MacAIAgent


def test_enhanced_safety_features():
    """Test all enhanced safety features."""
    print("🛡️ Testing Enhanced Safety Features")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test 1: Environment Detection
    print("1. Testing environment detection...")
    app_info = agent.identify_current_application()
    
    if app_info["success"]:
        print(f"✅ Environment detection working")
        print(f"   Protected app: {app_info['is_protected_app']}")
        if app_info['is_protected_app']:
            print("   🛡️ Development environment protection ACTIVE")
    else:
        print(f"⚠️ Environment detection limited (LLM vision not available)")
    
    # Test 2: Dangerous Action Detection
    print("\n2. Testing dangerous action detection...")
    dangerous_actions = [
        ("close Visual Studio Code", None),
        ("quit Terminal", None),
        ("click close button", (20, 20)),
        ("delete important files", None)
    ]
    
    for action, coords in dangerous_actions:
        safety = agent.screen.is_dangerous_action(action, coords)
        status = "🚨 BLOCKED" if safety['is_dangerous'] else "✅ SAFE"
        print(f"   '{action}': {status}")
    
    print("✅ Safety features test complete")


def test_enhanced_mouse_interactions():
    """Test enhanced mouse interaction capabilities."""
    print("\n🖱️ Testing Enhanced Mouse Interactions")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test 1: Multi-click with safety
    print("1. Testing multi-click safety...")
    safe_coordinates = [(500, 400), (600, 400)]  # Center area clicks
    dangerous_coordinates = [(20, 20), (30, 30)]  # Close button area
    
    print("   Testing safe coordinates...")
    safe_results = agent.screen.multi_click(safe_coordinates, interval=0.1)
    safe_count = sum(1 for r in safe_results if r['success'])
    print(f"   ✅ Safe clicks executed: {safe_count}/{len(safe_coordinates)}")
    
    print("   Testing dangerous coordinates...")
    dangerous_results = agent.screen.multi_click(dangerous_coordinates, interval=0.1)
    blocked_count = sum(1 for r in dangerous_results if not r['success'] and 'safety' in r.get('reason', ''))
    print(f"   🛡️ Dangerous clicks blocked: {blocked_count}/{len(dangerous_coordinates)}")
    
    # Test 2: Hover functionality
    print("\n2. Testing hover functionality...")
    current_pos = agent.screen.get_mouse_position()
    print(f"   Current mouse position: {current_pos}")
    
    # Hover at center of screen
    center_x, center_y = agent.screen.screen_width // 2, agent.screen.screen_height // 2
    print(f"   Hovering at center: ({center_x}, {center_y})")
    agent.screen.hover_mouse(center_x, center_y, duration=0.5)
    
    # Return to original position
    agent.screen.move_mouse(current_pos[0], current_pos[1])
    print("   ✅ Hover test complete")


def test_intelligent_app_opening():
    """Test the intelligent app opening workflow."""
    print("\n🔍 Testing Intelligent App Opening")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    print("This test will demonstrate the complete app opening workflow:")
    print("• Environment analysis")
    print("• Safety checks")
    print("• Spotlight search (Cmd+Space)")
    print("• Visual verification")
    print("• Success confirmation")
    
    app_to_open = "Calculator"
    confirm = input(f"\nOpen {app_to_open} for testing? (y/n): ").lower().strip()
    
    if confirm in ['y', 'yes']:
        print(f"\n🚀 Opening {app_to_open} with full workflow...")
        
        # Use the enhanced opening method
        result = agent.open_application(app_to_open)
        
        if result["success"]:
            print(f"✅ {app_to_open} opening workflow completed")
            print(f"   Screenshots taken: {len([k for k in result.keys() if 'screenshot' in k])}")
            print(f"   Visual verification: {'✅ Available' if 'analysis' in str(result) else '⚠️ Limited'}")
        else:
            print(f"❌ {app_to_open} opening failed: {result.get('message', 'Unknown error')}")
    else:
        print("⏭️ Skipping app opening test")


def test_keyboard_shortcuts():
    """Test Mac-specific keyboard shortcuts."""
    print("\n⌨️ Testing Keyboard Shortcuts")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    # Test safe shortcuts
    print("1. Testing safe keyboard shortcuts...")
    safe_shortcuts = [
        ("cmd", "space"),  # Spotlight (will actually execute)
        ("cmd", "tab"),    # App switcher
    ]
    
    for shortcut in safe_shortcuts:
        action_desc = f"keyboard shortcut {' + '.join(shortcut)}"
        safety = agent.screen.is_dangerous_action(action_desc)
        
        if not safety['is_dangerous']:
            print(f"   ✅ {' + '.join(shortcut)}: Safe to execute")
        else:
            print(f"   ⚠️ {' + '.join(shortcut)}: Flagged as potentially dangerous")
    
    # Test dangerous shortcuts (won't execute)
    print("\n2. Testing dangerous keyboard shortcuts...")
    dangerous_shortcuts = [
        ("cmd", "q"),      # Quit
        ("cmd", "w"),      # Close window
        ("cmd", "option", "esc"),  # Force quit
    ]
    
    for shortcut in dangerous_shortcuts:
        action_desc = f"keyboard shortcut {' + '.join(shortcut)}"
        safety = agent.screen.is_dangerous_action(action_desc)
        
        if safety['is_dangerous']:
            print(f"   🛡️ {' + '.join(shortcut)}: Correctly flagged as dangerous")
        else:
            print(f"   ⚠️ {' + '.join(shortcut)}: Should be flagged as dangerous!")


def test_screenshot_analysis():
    """Test screenshot analysis capabilities."""
    print("\n📸 Testing Screenshot Analysis")
    print("=" * 50)
    
    agent = MacAIAgent()
    
    print("1. Taking and analyzing current screen...")
    screenshot_path = agent.take_screenshot_with_timestamp()
    print(f"   Screenshot saved: {screenshot_path}")
    
    # Test basic analysis
    analysis = agent.analyze_screen("What applications are visible on the screen?")
    
    if analysis:
        print("   ✅ Screen analysis successful")
        print(f"   🔍 Analysis preview: {analysis[:100]}...")
    else:
        print("   ⚠️ Screen analysis limited (LLM vision not available)")
    
    print("✅ Screenshot analysis test complete")


def main():
    """Run comprehensive enhanced system tests."""
    print("🤖 Mac AI Agent - Enhanced System Test Suite")
    print("=" * 60)
    print("\nThis test suite validates all enhanced features:")
    print("• 🛡️ Development environment protection")
    print("• 🖱️ Enhanced mouse interactions")
    print("• ⌨️ Mac-specific keyboard shortcuts")
    print("• 📸 AI-powered screenshot analysis")
    print("• 🔍 Intelligent app opening workflow")
    print("• ⚠️ Comprehensive safety checks")
    
    try:
        test_enhanced_safety_features()
        test_enhanced_mouse_interactions()
        test_keyboard_shortcuts()
        test_screenshot_analysis()
        test_intelligent_app_opening()
        
        print("\n🎉 Enhanced System Test Suite Complete!")
        print("\n📊 Summary of Enhanced Features:")
        print("✅ Development environment protection")
        print("✅ Dangerous action detection and blocking")
        print("✅ Multi-click with safety checks")
        print("✅ Mouse hover and advanced interactions")
        print("✅ Mac-specific keyboard shortcut handling")
        print("✅ AI-powered screenshot analysis")
        print("✅ Intelligent app opening workflow")
        print("✅ Visual verification system")
        
        print("\n🛡️ Your development environment is protected!")
        print("The agent will now:")
        print("• Detect when VS Code or Terminal is active")
        print("• Warn before dangerous actions")
        print("• Require confirmation for high-risk operations")
        print("• Take screenshots to verify actions")
        print("• Use AI vision to understand screen content")
        
    except KeyboardInterrupt:
        print("\n\n👋 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")


if __name__ == "__main__":
    main()
