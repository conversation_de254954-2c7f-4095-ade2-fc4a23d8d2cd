"""
Core screen interaction functionality for Mac AI Agent
"""

import time
import pyautogui
from PIL import Image
import numpy as np
from typing import Tuple, Optional, Union, List
from pathlib import Path


class ScreenInteraction:
    """
    Core class for screen interaction capabilities including clicking, dragging,
    screenshots, and basic keyboard/mouse operations.
    """

    def __init__(self):
        """Initialize the screen interaction system."""
        # Configure PyAutoGUI for Mac
        pyautogui.FAILSAFE = True  # Move mouse to top-left corner to abort
        pyautogui.PAUSE = 0.1  # Small pause between actions

        # Get screen dimensions
        self.screen_width, self.screen_height = pyautogui.size()

        # Define protected applications (home environment)
        self.protected_apps = {
            'Visual Studio Code', 'Code', 'VSCode', 'vscode',
            'Terminal', 'iTerm2', 'iTerm', 'Hyper',
            'PyCharm', 'IntelliJ IDEA', 'Xcode',
            'Sublime Text', 'Atom', 'Vim', 'Emacs'
        }

        # Define dangerous UI elements to avoid
        self.dangerous_elements = {
            'close_button': ['close', 'exit', 'quit', '×', '✕'],
            'delete_actions': ['delete', 'remove', 'trash', 'destroy'],
            'system_actions': ['shutdown', 'restart', 'log out', 'sleep']
        }
        
    def take_screenshot(self, region: Optional[Tuple[int, int, int, int]] = None) -> Image.Image:
        """
        Take a screenshot of the screen or a specific region.
        
        Args:
            region: Optional tuple (left, top, width, height) for partial screenshot
            
        Returns:
            PIL Image object of the screenshot
        """
        if region:
            screenshot = pyautogui.screenshot(region=region)
        else:
            screenshot = pyautogui.screenshot()
        return screenshot
    
    def save_screenshot(self, filename: str, region: Optional[Tuple[int, int, int, int]] = None) -> str:
        """
        Take and save a screenshot to file.
        
        Args:
            filename: Path to save the screenshot
            region: Optional region to capture
            
        Returns:
            Path to the saved screenshot
        """
        screenshot = self.take_screenshot(region)
        screenshot.save(filename)
        return filename
    
    def click(self, x: int, y: int, button: str = 'left', clicks: int = 1, interval: float = 0.0) -> None:
        """
        Click at the specified coordinates.
        
        Args:
            x: X coordinate
            y: Y coordinate
            button: Mouse button ('left', 'right', 'middle')
            clicks: Number of clicks
            interval: Interval between clicks
        """
        pyautogui.click(x, y, clicks=clicks, interval=interval, button=button)
    
    def double_click(self, x: int, y: int) -> None:
        """Double-click at the specified coordinates."""
        pyautogui.doubleClick(x, y)
    
    def right_click(self, x: int, y: int) -> None:
        """Right-click at the specified coordinates."""
        pyautogui.rightClick(x, y)
    
    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, 
             duration: float = 1.0, button: str = 'left') -> None:
        """
        Drag from start coordinates to end coordinates.
        
        Args:
            start_x: Starting X coordinate
            start_y: Starting Y coordinate
            end_x: Ending X coordinate
            end_y: Ending Y coordinate
            duration: Duration of the drag in seconds
            button: Mouse button to use for dragging
        """
        pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration, button=button)
    
    def move_mouse(self, x: int, y: int, duration: float = 0.0) -> None:
        """
        Move mouse to specified coordinates.
        
        Args:
            x: X coordinate
            y: Y coordinate
            duration: Duration of the movement in seconds
        """
        pyautogui.moveTo(x, y, duration=duration)
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """Get current mouse position."""
        return pyautogui.position()
    
    def scroll(self, clicks: int, x: Optional[int] = None, y: Optional[int] = None) -> None:
        """
        Scroll at the current mouse position or specified coordinates.
        
        Args:
            clicks: Number of scroll clicks (positive for up, negative for down)
            x: Optional X coordinate
            y: Optional Y coordinate
        """
        if x is not None and y is not None:
            pyautogui.scroll(clicks, x=x, y=y)
        else:
            pyautogui.scroll(clicks)
    
    def type_text(self, text: str, interval: float = 0.0) -> None:
        """
        Type text at the current cursor position.
        
        Args:
            text: Text to type
            interval: Interval between keystrokes
        """
        pyautogui.write(text, interval=interval)
    
    def press_key(self, key: str) -> None:
        """
        Press a single key.
        
        Args:
            key: Key name (e.g., 'enter', 'space', 'tab', 'esc')
        """
        pyautogui.press(key)
    
    def key_combo(self, *keys: str) -> None:
        """
        Press a combination of keys simultaneously.

        Args:
            keys: Keys to press together (e.g., 'cmd', 'c' for copy)
        """
        # Normalize Mac-specific key names
        normalized_keys = []
        for key in keys:
            key_lower = key.lower()
            if key_lower in ['command', 'cmd']:
                normalized_keys.append('cmd')
            elif key_lower in ['option', 'alt']:
                normalized_keys.append('option')
            elif key_lower in ['control', 'ctrl']:
                normalized_keys.append('ctrl')
            elif key_lower in ['shift']:
                normalized_keys.append('shift')
            else:
                normalized_keys.append(key)

        pyautogui.hotkey(*normalized_keys)
    
    def hold_key(self, key: str) -> None:
        """Hold down a key (use release_key to release)."""
        pyautogui.keyDown(key)
    
    def release_key(self, key: str) -> None:
        """Release a held key."""
        pyautogui.keyUp(key)
    
    def find_image_on_screen(self, image_path: str, confidence: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Find an image on the screen and return its center coordinates.
        
        Args:
            image_path: Path to the image to find
            confidence: Confidence threshold (0.0 to 1.0)
            
        Returns:
            Tuple of (x, y) coordinates if found, None otherwise
        """
        try:
            location = pyautogui.locateOnScreen(image_path, confidence=confidence)
            if location:
                return pyautogui.center(location)
        except pyautogui.ImageNotFoundException:
            pass
        return None
    
    def click_image(self, image_path: str, confidence: float = 0.8) -> bool:
        """
        Find and click on an image on the screen.
        
        Args:
            image_path: Path to the image to find and click
            confidence: Confidence threshold
            
        Returns:
            True if image was found and clicked, False otherwise
        """
        location = self.find_image_on_screen(image_path, confidence)
        if location:
            self.click(location[0], location[1])
            return True
        return False
    
    def wait_for_image(self, image_path: str, timeout: float = 10.0, confidence: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Wait for an image to appear on screen.
        
        Args:
            image_path: Path to the image to wait for
            timeout: Maximum time to wait in seconds
            confidence: Confidence threshold
            
        Returns:
            Tuple of (x, y) coordinates if found within timeout, None otherwise
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            location = self.find_image_on_screen(image_path, confidence)
            if location:
                return location
            time.sleep(0.5)
        return None
    
    def get_pixel_color(self, x: int, y: int) -> Tuple[int, int, int]:
        """
        Get the RGB color of a pixel at the specified coordinates.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            RGB tuple (r, g, b)
        """
        screenshot = self.take_screenshot()
        return screenshot.getpixel((x, y))
    
    def is_color_at_position(self, x: int, y: int, expected_color: Tuple[int, int, int], 
                           tolerance: int = 0) -> bool:
        """
        Check if a specific color exists at the given position.
        
        Args:
            x: X coordinate
            y: Y coordinate
            expected_color: Expected RGB color tuple
            tolerance: Color tolerance (0-255)
            
        Returns:
            True if color matches within tolerance
        """
        actual_color = self.get_pixel_color(x, y)
        if tolerance == 0:
            return actual_color == expected_color
        
        return all(abs(actual - expected) <= tolerance
                  for actual, expected in zip(actual_color, expected_color))

    def is_protected_app_active(self) -> bool:
        """
        Check if a protected application (development environment) is currently active.

        Returns:
            True if a protected app is active
        """
        try:
            # Take a screenshot to analyze
            screenshot = self.take_screenshot()

            # This is a basic implementation - in a real system you'd use more sophisticated detection
            # For now, we'll rely on the LLM analysis in the core agent
            return False  # Will be enhanced with LLM vision analysis

        except Exception:
            return False

    def is_dangerous_action(self, action_description: str, target_coordinates: tuple = None) -> dict:
        """
        Check if an action might be dangerous to the development environment.

        Args:
            action_description: Description of the action to perform
            target_coordinates: Optional coordinates where action will be performed

        Returns:
            Dictionary with safety assessment
        """
        action_lower = action_description.lower()

        # Check for dangerous keywords
        dangerous_keywords = []
        for category, keywords in self.dangerous_elements.items():
            for keyword in keywords:
                if keyword in action_lower:
                    dangerous_keywords.append((category, keyword))

        # Check for protected app mentions
        protected_mentions = []
        for app in self.protected_apps:
            if app.lower() in action_lower:
                protected_mentions.append(app)

        is_dangerous = len(dangerous_keywords) > 0 or len(protected_mentions) > 0

        # Check coordinates if provided (close button area detection)
        coordinate_risk = False
        if target_coordinates:
            x, y = target_coordinates
            # Top-left corner area (typical close button location)
            if x < 50 and y < 50:
                coordinate_risk = True

        return {
            'is_dangerous': is_dangerous or coordinate_risk,
            'risk_level': 'high' if is_dangerous else ('medium' if coordinate_risk else 'low'),
            'dangerous_keywords': dangerous_keywords,
            'protected_apps': protected_mentions,
            'coordinate_risk': coordinate_risk,
            'warning_message': self._generate_safety_warning(dangerous_keywords, protected_mentions, coordinate_risk)
        }

    def _generate_safety_warning(self, dangerous_keywords, protected_mentions, coordinate_risk):
        """Generate a safety warning message."""
        warnings = []

        if dangerous_keywords:
            warnings.append(f"⚠️ Detected dangerous action keywords: {[kw[1] for kw in dangerous_keywords]}")

        if protected_mentions:
            warnings.append(f"⚠️ Action involves protected development apps: {protected_mentions}")

        if coordinate_risk:
            warnings.append("⚠️ Click target is near window close button area")

        if warnings:
            return "🛡️ SAFETY WARNING:\n" + "\n".join(warnings) + "\n⚠️ This action might interfere with your development environment!"

        return None

    def smart_click(self, description: str, screenshot_path: str = None, llm_client=None) -> dict:
        """
        Perform an intelligent click based on description and visual analysis.

        Args:
            description: Description of what to click (e.g., "close button", "search bar")
            screenshot_path: Optional path to screenshot to analyze
            llm_client: LLM client for vision analysis

        Returns:
            Dictionary with click result and coordinates
        """
        if not screenshot_path:
            screenshot_path = f"screenshots/temp_screenshot_{int(time.time())}.png"
            self.save_screenshot(screenshot_path)

        if not llm_client:
            return {
                "success": False,
                "message": "Smart click requires LLM vision analysis - no LLM client provided",
                "description": description,
                "screenshot_path": screenshot_path
            }

        # Use LLM vision to analyze the screenshot and find the element
        analysis_prompt = f"""Analyze this screenshot and help me click on: "{description}"

Please provide:
1. Can you see the element I want to click on?
2. Describe where it is located on the screen (top, bottom, left, right, center)
3. What does it look like (color, shape, text, icon)?
4. Estimate the approximate coordinates where I should click (as percentage of screen width/height)
5. Is it safe to click on this element?

For coordinates, please provide your best estimate as percentages:
- X coordinate: 0% (far left) to 100% (far right)
- Y coordinate: 0% (top) to 100% (bottom)

Example response format:
"I can see [description of element]. It's located in the [position] of the screen.
The element appears to be [description of appearance].
Estimated click coordinates: X: 45%, Y: 30%
Safety: [safe/unsafe and why]"
"""

        try:
            analysis = llm_client.analyze_image(screenshot_path, analysis_prompt)

            if not analysis:
                return {
                    "success": False,
                    "message": "Could not analyze screenshot with LLM vision",
                    "description": description,
                    "screenshot_path": screenshot_path
                }

            # Parse the analysis to extract coordinates
            coordinates = self._parse_coordinates_from_analysis(analysis)

            if not coordinates:
                return {
                    "success": False,
                    "message": "Could not extract coordinates from LLM analysis",
                    "description": description,
                    "screenshot_path": screenshot_path,
                    "analysis": analysis
                }

            x, y = coordinates

            # Safety check before clicking
            safety_check = self.is_dangerous_action(f"click on {description}", (x, y))

            if safety_check['is_dangerous']:
                return {
                    "success": False,
                    "message": "Click blocked by safety check",
                    "description": description,
                    "coordinates": (x, y),
                    "safety_warning": safety_check['warning_message'],
                    "analysis": analysis
                }

            # Perform the click
            self.click(x, y)

            # Take a screenshot after clicking to verify
            time.sleep(0.5)  # Brief pause for UI to respond
            after_screenshot = f"screenshots/after_click_{int(time.time())}.png"
            self.save_screenshot(after_screenshot)

            return {
                "success": True,
                "message": f"Successfully clicked on {description}",
                "description": description,
                "coordinates": (x, y),
                "before_screenshot": screenshot_path,
                "after_screenshot": after_screenshot,
                "analysis": analysis
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"Error during smart click: {str(e)}",
                "description": description,
                "screenshot_path": screenshot_path,
                "error": str(e)
            }

    def hover_mouse(self, x: int, y: int, duration: float = 1.0) -> None:
        """
        Hover mouse over coordinates for a specified duration.

        Args:
            x: X coordinate
            y: Y coordinate
            duration: How long to hover in seconds
        """
        self.move_mouse(x, y, duration=0.5)
        time.sleep(duration)

    def click_and_drag_select(self, start_x: int, start_y: int, end_x: int, end_y: int) -> None:
        """
        Click and drag to select an area (like selecting text or files).

        Args:
            start_x: Starting X coordinate
            start_y: Starting Y coordinate
            end_x: Ending X coordinate
            end_y: Ending Y coordinate
        """
        # Move to start position
        self.move_mouse(start_x, start_y)
        time.sleep(0.1)

        # Hold down mouse button and drag
        pyautogui.mouseDown()
        self.move_mouse(end_x, end_y, duration=0.5)
        pyautogui.mouseUp()

    def multi_click(self, coordinates_list: list, interval: float = 0.5) -> list:
        """
        Perform multiple clicks in sequence.

        Args:
            coordinates_list: List of (x, y) tuples
            interval: Time between clicks

        Returns:
            List of results for each click
        """
        results = []

        for i, (x, y) in enumerate(coordinates_list):
            try:
                # Check safety for each click
                safety_check = self.is_dangerous_action(f"click at ({x}, {y})", (x, y))

                if safety_check['is_dangerous']:
                    results.append({
                        "index": i,
                        "coordinates": (x, y),
                        "success": False,
                        "reason": "Blocked by safety check",
                        "warning": safety_check['warning_message']
                    })
                    continue

                self.click(x, y)
                results.append({
                    "index": i,
                    "coordinates": (x, y),
                    "success": True
                })

                if i < len(coordinates_list) - 1:  # Don't wait after last click
                    time.sleep(interval)

            except Exception as e:
                results.append({
                    "index": i,
                    "coordinates": (x, y),
                    "success": False,
                    "error": str(e)
                })

        return results

    def find_and_click_color(self, target_color: tuple, tolerance: int = 10,
                           region: tuple = None) -> bool:
        """
        Find a specific color on screen and click on it.

        Args:
            target_color: RGB color tuple to find
            tolerance: Color matching tolerance
            region: Optional region to search in (left, top, width, height)

        Returns:
            True if color found and clicked
        """
        screenshot = self.take_screenshot(region)

        # Convert to numpy array for color searching
        import numpy as np
        screenshot_array = np.array(screenshot)

        # Find pixels matching the target color within tolerance
        if len(screenshot_array.shape) == 3:  # RGB image
            color_diff = np.abs(screenshot_array - target_color)
            matches = np.all(color_diff <= tolerance, axis=2)

            if np.any(matches):
                # Find the first match
                match_coords = np.where(matches)
                y, x = match_coords[0][0], match_coords[1][0]

                # Adjust coordinates if region was specified
                if region:
                    x += region[0]
                    y += region[1]

                # Safety check before clicking
                safety_check = self.is_dangerous_action(f"click on color {target_color}", (x, y))
                if safety_check['is_dangerous']:
                    print(safety_check['warning_message'])
                    return False

                self.click(x, y)
                return True

        return False

    def _parse_coordinates_from_analysis(self, analysis: str) -> tuple:
        """
        Parse coordinates from LLM analysis text.

        Args:
            analysis: LLM analysis text containing coordinate information

        Returns:
            Tuple of (x, y) pixel coordinates, or None if not found
        """
        import re

        try:
            # Look for percentage coordinates in various formats
            patterns = [
                r'X:\s*(\d+(?:\.\d+)?)%.*?Y:\s*(\d+(?:\.\d+)?)%',  # X: 45%, Y: 30%
                r'x:\s*(\d+(?:\.\d+)?)%.*?y:\s*(\d+(?:\.\d+)?)%',  # x: 45%, y: 30%
                r'(\d+(?:\.\d+)?)%.*?(\d+(?:\.\d+)?)%',             # 45% ... 30%
                r'coordinates?.*?(\d+(?:\.\d+)?)%.*?(\d+(?:\.\d+)?)%'  # coordinates: 45%, 30%
            ]

            for pattern in patterns:
                match = re.search(pattern, analysis, re.IGNORECASE)
                if match:
                    x_percent = float(match.group(1))
                    y_percent = float(match.group(2))

                    # Convert percentages to pixel coordinates
                    x = int((x_percent / 100.0) * self.screen_width)
                    y = int((y_percent / 100.0) * self.screen_height)

                    # Ensure coordinates are within screen bounds
                    x = max(0, min(x, self.screen_width - 1))
                    y = max(0, min(y, self.screen_height - 1))

                    return (x, y)

            # If no percentage coordinates found, look for pixel coordinates
            pixel_patterns = [
                r'(\d+),\s*(\d+)',  # 500, 300
                r'\((\d+),\s*(\d+)\)',  # (500, 300)
                r'x:\s*(\d+).*?y:\s*(\d+)',  # x: 500, y: 300
            ]

            for pattern in pixel_patterns:
                match = re.search(pattern, analysis, re.IGNORECASE)
                if match:
                    x = int(match.group(1))
                    y = int(match.group(2))

                    # Ensure coordinates are within screen bounds
                    if 0 <= x < self.screen_width and 0 <= y < self.screen_height:
                        return (x, y)

            return None

        except Exception as e:
            print(f"Error parsing coordinates: {e}")
            return None

    def visual_click_with_verification(self, description: str, llm_client=None, max_attempts: int = 3) -> dict:
        """
        Perform a visual click with multiple attempts and verification.

        Args:
            description: Description of what to click
            llm_client: LLM client for vision analysis
            max_attempts: Maximum number of click attempts

        Returns:
            Dictionary with final result
        """
        if not llm_client:
            return {
                "success": False,
                "message": "Visual click requires LLM client for vision analysis"
            }

        for attempt in range(max_attempts):
            print(f"🎯 Attempt {attempt + 1}/{max_attempts}: Looking for '{description}'")

            # Take screenshot and attempt click
            result = self.smart_click(description, llm_client=llm_client)

            if not result["success"]:
                print(f"❌ Attempt {attempt + 1} failed: {result['message']}")
                if attempt < max_attempts - 1:
                    time.sleep(1)  # Wait before retry
                continue

            # Verify the click worked by analyzing the after screenshot
            verification_prompt = f"""I just clicked on "{description}" in this screenshot.

Please analyze what happened:
1. Did the click appear to work successfully?
2. What changes do you see on the screen?
3. Did any new windows, dialogs, or UI elements appear?
4. Are there any error messages or unexpected results?
5. Does the screen state look correct for what was clicked?

Please respond with one of:
- SUCCESS: [brief description of what happened]
- FAILED: [brief description of what went wrong]
- UNCLEAR: [brief description of uncertainty]
"""

            try:
                verification = llm_client.analyze_image(result["after_screenshot"], verification_prompt)

                if verification and "success" in verification.lower():
                    print(f"✅ Click verification successful!")
                    result["verification"] = verification
                    result["attempts"] = attempt + 1
                    return result
                else:
                    print(f"⚠️ Click verification unclear: {verification[:100] if verification else 'No verification'}")
                    if attempt < max_attempts - 1:
                        print("🔄 Retrying...")
                        time.sleep(1)

            except Exception as e:
                print(f"❌ Verification error: {e}")

        return {
            "success": False,
            "message": f"Failed to successfully click '{description}' after {max_attempts} attempts",
            "attempts": max_attempts
        }
